<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            color: #1890ff;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .result {
            background: #f6f8fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #52c41a;
        }
        .error {
            border-left: 4px solid #ff4d4f;
            background: #fff2f0;
        }
        .loading {
            color: #1890ff;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        .member-card {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            background: #fafafa;
        }
        .member-name {
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 5px;
        }
        .member-role {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .research-direction {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>团队成员 API 测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 测试基本 API 连接</div>
            <button onclick="testBasicAPI()">测试基本连接</button>
            <div id="basic-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 测试获取所有团队成员</div>
            <button onclick="testAllMembers()">获取所有成员</button>
            <div id="all-members-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 测试按角色获取成员 (Mentor)</div>
            <button onclick="testMentorMembers()">获取 Mentor 成员</button>
            <div id="mentor-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 测试 Rich Text 渲染</div>
            <button onclick="testRichTextRendering()">测试 Rich Text</button>
            <div id="richtext-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:1337';

        function showLoading(elementId) {
            document.getElementById(elementId).innerHTML = '正在加载...';
            document.getElementById(elementId).className = 'result loading';
        }

        function showSuccess(elementId, data) {
            document.getElementById(elementId).innerHTML = JSON.stringify(data, null, 2);
            document.getElementById(elementId).className = 'result success';
        }

        function showError(elementId, error) {
            document.getElementById(elementId).innerHTML = `错误: ${error.message}\n\n${JSON.stringify(error, null, 2)}`;
            document.getElementById(elementId).className = 'result error';
        }

        async function testBasicAPI() {
            showLoading('basic-result');
            try {
                const response = await fetch(`${API_BASE}/api/team-members`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                showSuccess('basic-result', {
                    status: 'success',
                    totalMembers: data.data.length,
                    pagination: data.meta.pagination
                });
            } catch (error) {
                showError('basic-result', error);
            }
        }

        async function testAllMembers() {
            showLoading('all-members-result');
            try {
                const response = await fetch(`${API_BASE}/api/team-members?populate=avatar`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                
                // 显示成员信息
                let html = `<div>总共 ${data.data.length} 个成员:</div>\n\n`;
                data.data.forEach(member => {
                    html += `<div class="member-card">
                        <div class="member-name">${member.name}</div>
                        <div class="member-role">${member.role} - ${member.title}</div>
                        <div class="research-direction">
                            <strong>研究方向:</strong><br>
                            ${JSON.stringify(member.researchDirection, null, 2)}
                        </div>
                    </div>`;
                });
                
                document.getElementById('all-members-result').innerHTML = html;
                document.getElementById('all-members-result').className = 'result success';
            } catch (error) {
                showError('all-members-result', error);
            }
        }

        async function testMentorMembers() {
            showLoading('mentor-result');
            try {
                const response = await fetch(`${API_BASE}/api/team-members?filters[role][$eq]=Mentor&populate=avatar`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                showSuccess('mentor-result', data);
            } catch (error) {
                showError('mentor-result', error);
            }
        }

        async function testRichTextRendering() {
            showLoading('richtext-result');
            try {
                const response = await fetch(`${API_BASE}/api/team-members?filters[role][$eq]=Mentor&populate=avatar`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                
                let html = '<div>Rich Text 数据结构测试:</div>\n\n';
                data.data.forEach(member => {
                    html += `<div class="member-card">
                        <div class="member-name">${member.name}</div>
                        <div class="research-direction">
                            <strong>研究方向 (Blocks 格式):</strong><br>
                            ${renderBlocks(member.researchDirection)}
                        </div>
                    </div>`;
                });
                
                document.getElementById('richtext-result').innerHTML = html;
                document.getElementById('richtext-result').className = 'result success';
            } catch (error) {
                showError('richtext-result', error);
            }
        }

        function renderBlocks(blocks) {
            if (!blocks || !Array.isArray(blocks)) {
                return '<em>暂无信息</em>';
            }
            
            let html = '';
            blocks.forEach(block => {
                switch (block.type) {
                    case 'paragraph':
                        html += '<p>' + renderChildren(block.children) + '</p>';
                        break;
                    case 'list':
                        const tag = block.format === 'ordered' ? 'ol' : 'ul';
                        html += `<${tag}>`;
                        block.children.forEach(item => {
                            if (item.type === 'list-item') {
                                html += '<li>' + renderChildren(item.children) + '</li>';
                            }
                        });
                        html += `</${tag}>`;
                        break;
                    case 'heading':
                        const level = block.level || 1;
                        html += `<h${level}>` + renderChildren(block.children) + `</h${level}>`;
                        break;
                    default:
                        html += '<div>' + JSON.stringify(block) + '</div>';
                }
            });
            return html;
        }

        function renderChildren(children) {
            if (!children || !Array.isArray(children)) {
                return '';
            }
            
            return children.map(child => {
                if (child.type === 'text') {
                    let text = child.text;
                    if (child.bold) text = `<strong>${text}</strong>`;
                    if (child.italic) text = `<em>${text}</em>`;
                    return text;
                }
                return child.text || '';
            }).join('');
        }

        // 页面加载时自动测试基本连接
        window.onload = function() {
            testBasicAPI();
        };
    </script>
</body>
</html>
