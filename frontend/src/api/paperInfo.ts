import api from './config';
import type { PaperInfo, StrapiPaperInfoResponse, PaginationParams } from '../types';

class PaperInfoService {
  /**
   * 获取所有论文信息
   * @param params 分页和过滤参数
   * @returns 论文信息列表
   */
  async getAllPaperInfos(params?: PaginationParams): Promise<PaperInfo[]> {
    try {
      const queryParams = new URLSearchParams();
      
      // 设置分页参数
      if (params?.page) {
        queryParams.append('pagination[page]', params.page.toString());
      }
      if (params?.pageSize) {
        queryParams.append('pagination[pageSize]', params.pageSize.toString());
      }
      
      // 设置排序参数 - 默认按年份降序排列
      const sortParam = params?.sort || 'year:desc,createdAt:desc';
      queryParams.append('sort', sortParam);
      
      // 设置过滤参数
      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            queryParams.append(`filters[${key}]`, value.toString());
          }
        });
      }
      
      // 只获取已发布的内容
      queryParams.append('publicationState', 'live');
      
      // 添加调试日志
      const requestUrl = `/api/paper-infos?${queryParams.toString()}`;
      console.log('PaperInfo API 请求 URL:', requestUrl);
      console.log('请求参数:', Object.fromEntries(queryParams.entries()));

      const response = await api.get<StrapiPaperInfoResponse>(requestUrl);
      
      console.log('PaperInfo API 响应:', response.data);
      
      return response.data.data || [];
    } catch (error) {
      console.error('获取论文信息列表失败:', error);
      if (error.response) {
        console.error('错误响应状态:', error.response.status);
        console.error('错误响应数据:', error.response.data);
        console.error('错误响应头:', error.response.headers);
      } else if (error.request) {
        console.error('请求错误:', error.request);
      } else {
        console.error('错误信息:', error.message);
      }
      throw error;
    }
  }

  /**
   * 根据ID获取单个论文信息
   * @param id 论文信息ID
   * @returns 论文信息详情
   */
  async getPaperInfoById(id: number): Promise<PaperInfo> {
    try {
      const response = await api.get<{ data: PaperInfo }>(`/api/paper-infos/${id}`);
      return response.data.data;
    } catch (error) {
      console.error(`获取论文信息详情失败 (ID: ${id}):`, error);
      throw error;
    }
  }

  /**
   * 搜索论文信息
   * @param searchTerm 搜索关键词
   * @param params 其他参数
   * @returns 搜索结果
   */
  async searchPaperInfos(searchTerm: string, params?: PaginationParams): Promise<PaperInfo[]> {
    try {
      const searchParams = {
        ...params,
        filters: {
          ...params?.filters,
          $or: [
            { title: { $containsi: searchTerm } },
            { authors: { $containsi: searchTerm } },
            { journal: { $containsi: searchTerm } },
            { abstract: { $containsi: searchTerm } }
          ]
        }
      };
      
      return this.getAllPaperInfos(searchParams);
    } catch (error) {
      console.error('搜索论文信息失败:', error);
      throw error;
    }
  }

  /**
   * 按年份获取论文信息
   * @param year 年份
   * @param params 其他参数
   * @returns 指定年份的论文信息
   */
  async getPaperInfosByYear(year: number, params?: PaginationParams): Promise<PaperInfo[]> {
    try {
      const yearParams = {
        ...params,
        filters: {
          ...params?.filters,
          year: { $eq: year }
        }
      };
      
      return this.getAllPaperInfos(yearParams);
    } catch (error) {
      console.error(`获取${year}年论文信息失败:`, error);
      throw error;
    }
  }

  /**
   * 获取所有可用的年份列表
   * @returns 年份列表（降序排列）
   */
  async getAvailableYears(): Promise<number[]> {
    try {
      // 获取所有论文信息，只需要年份字段
      const papers = await this.getAllPaperInfos({
        pageSize: 1000, // 获取足够多的数据以确保包含所有年份
        sort: 'year:desc'
      });
      
      // 提取年份并去重
      const years = papers
        .map(paper => paper.year)
        .filter((year): year is number => year !== undefined && year !== null)
        .filter((year, index, array) => array.indexOf(year) === index) // 去重
        .sort((a, b) => b - a); // 降序排列
      
      return years;
    } catch (error) {
      console.error('获取可用年份列表失败:', error);
      throw error;
    }
  }
}

export default new PaperInfoService();
