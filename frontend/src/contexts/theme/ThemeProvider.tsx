import React, { useState, useEffect, ReactNode } from 'react';
import type { ThemeMode, ThemeContextType } from '../../types';
import { ThemeContext } from './ThemeContext';
import { themes, defaultTheme, getCSSVariables } from './themeConfig';

interface ThemeProviderProps {
  children: ReactNode;
}

// 本地存储键名
const THEME_STORAGE_KEY = 'research-group-theme';

// 获取系统主题偏好
const getSystemTheme = (): ThemeMode => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
};

// 从本地存储获取主题
const getStoredTheme = (): ThemeMode | null => {
  if (typeof window !== 'undefined') {
    try {
      const stored = localStorage.getItem(THEME_STORAGE_KEY);
      if (stored && (stored === 'light' || stored === 'dark')) {
        return stored as ThemeMode;
      }
    } catch (error) {
      console.warn('Failed to read theme from localStorage:', error);
    }
  }
  return null;
};

// 保存主题到本地存储
const saveTheme = (mode: ThemeMode): void => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(THEME_STORAGE_KEY, mode);
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
  }
};

// 应用CSS变量到文档根元素
const applyCSSVariables = (mode: ThemeMode): void => {
  if (typeof window !== 'undefined' && document.documentElement) {
    const theme = themes[mode];
    const cssVariables = getCSSVariables(theme);
    
    Object.entries(cssVariables).forEach(([property, value]) => {
      document.documentElement.style.setProperty(property, value);
    });
    
    // 设置data-theme属性，便于CSS选择器使用
    document.documentElement.setAttribute('data-theme', mode);
  }
};

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // 初始化主题状态
  const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
    // 优先级：本地存储 > 系统偏好 > 默认主题
    const storedTheme = getStoredTheme();
    if (storedTheme) {
      return storedTheme;
    }
    return getSystemTheme();
  });

  // 当前主题对象
  const currentTheme = themes[themeMode];

  // 切换主题
  const toggleTheme = (): void => {
    const newMode = themeMode === 'light' ? 'dark' : 'light';
    setThemeMode(newMode);
  };

  // 设置特定主题
  const setTheme = (mode: ThemeMode): void => {
    setThemeMode(mode);
  };

  // 监听系统主题变化
  useEffect(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleSystemThemeChange = (e: MediaQueryListEvent) => {
        // 只有在没有用户手动设置主题时才跟随系统
        const storedTheme = getStoredTheme();
        if (!storedTheme) {
          setThemeMode(e.matches ? 'dark' : 'light');
        }
      };

      // 添加监听器
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleSystemThemeChange);
      } else {
        // 兼容旧版浏览器
        mediaQuery.addListener(handleSystemThemeChange);
      }

      // 清理函数
      return () => {
        if (mediaQuery.removeEventListener) {
          mediaQuery.removeEventListener('change', handleSystemThemeChange);
        } else {
          mediaQuery.removeListener(handleSystemThemeChange);
        }
      };
    }
  }, []);

  // 应用主题变化
  useEffect(() => {
    // 保存到本地存储
    saveTheme(themeMode);
    
    // 应用CSS变量
    applyCSSVariables(themeMode);
  }, [themeMode]);

  // 初始化时应用主题
  useEffect(() => {
    applyCSSVariables(themeMode);
  }, []);

  // 上下文值
  const contextValue: ThemeContextType = {
    theme: currentTheme,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
