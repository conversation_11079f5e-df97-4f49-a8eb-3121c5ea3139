import type { Theme, ThemeColors } from '../../types';

// 浅色主题颜色配置
const lightColors: ThemeColors = {
  // 基础颜色
  primary: '#007aff',
  secondary: '#5856d6',
  success: '#34c759',
  warning: '#ff9500',
  error: '#ff3b30',
  info: '#007aff',
  
  // 背景颜色
  background: {
    primary: '#fbfbfd',
    secondary: '#ffffff',
    tertiary: '#f2f2f7',
    elevated: '#ffffff',
    card: '#ffffff',
  },
  
  // 文字颜色
  text: {
    primary: '#1d1d1f',
    secondary: '#86868b',
    tertiary: '#c7c7cc',
    inverse: '#ffffff',
    disabled: '#c7c7cc',
  },
  
  // 边框颜色
  border: {
    primary: 'rgba(0, 0, 0, 0.08)',
    secondary: 'rgba(0, 0, 0, 0.04)',
    tertiary: '#d2d2d7',
  },
  
  // 阴影
  shadow: {
    sm: '0 2px 10px rgba(0, 0, 0, 0.08)',
    md: '0 4px 20px rgba(0, 0, 0, 0.12)',
    lg: '0 8px 40px rgba(0, 0, 0, 0.16)',
  },
  
  // 渐变
  gradient: {
    primary: 'linear-gradient(135deg, #007aff, #5856d6)',
    secondary: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
    hero: 'linear-gradient(135deg, rgba(0, 122, 255, 0.8), rgba(88, 86, 214, 0.8))',
  },
};

// 暗色主题颜色配置
const darkColors: ThemeColors = {
  // 基础颜色
  primary: '#0a84ff',
  secondary: '#5e5ce6',
  success: '#30d158',
  warning: '#ff9f0a',
  error: '#ff453a',
  info: '#0a84ff',
  
  // 背景颜色
  background: {
    primary: '#000000',
    secondary: '#1c1c1e',
    tertiary: '#2c2c2e',
    elevated: '#1c1c1e',
    card: '#1c1c1e',
  },
  
  // 文字颜色
  text: {
    primary: '#ffffff',
    secondary: '#ebebf5',
    tertiary: '#8e8e93',
    inverse: '#000000',
    disabled: '#48484a',
  },
  
  // 边框颜色
  border: {
    primary: 'rgba(255, 255, 255, 0.1)',
    secondary: 'rgba(255, 255, 255, 0.05)',
    tertiary: '#38383a',
  },
  
  // 阴影
  shadow: {
    sm: '0 2px 10px rgba(0, 0, 0, 0.3)',
    md: '0 4px 20px rgba(0, 0, 0, 0.4)',
    lg: '0 8px 40px rgba(0, 0, 0, 0.5)',
  },
  
  // 渐变
  gradient: {
    primary: 'linear-gradient(135deg, #0a84ff, #5e5ce6)',
    secondary: 'linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%)',
    hero: 'linear-gradient(135deg, rgba(10, 132, 255, 0.8), rgba(94, 92, 230, 0.8))',
  },
};

// 主题配置
export const lightTheme: Theme = {
  mode: 'light',
  colors: lightColors,
};

export const darkTheme: Theme = {
  mode: 'dark',
  colors: darkColors,
};

// 主题映射
export const themes = {
  light: lightTheme,
  dark: darkTheme,
};

// 默认主题
export const defaultTheme = lightTheme;

// CSS 变量映射函数
export const getCSSVariables = (theme: Theme): Record<string, string> => {
  const { colors } = theme;
  
  return {
    // 基础颜色
    '--color-primary': colors.primary,
    '--color-secondary': colors.secondary,
    '--color-success': colors.success,
    '--color-warning': colors.warning,
    '--color-error': colors.error,
    '--color-info': colors.info,
    
    // 背景颜色
    '--bg-primary': colors.background.primary,
    '--bg-secondary': colors.background.secondary,
    '--bg-tertiary': colors.background.tertiary,
    '--bg-elevated': colors.background.elevated,
    '--bg-card': colors.background.card,
    
    // 文字颜色
    '--text-primary': colors.text.primary,
    '--text-secondary': colors.text.secondary,
    '--text-tertiary': colors.text.tertiary,
    '--text-inverse': colors.text.inverse,
    '--text-disabled': colors.text.disabled,
    
    // 边框颜色
    '--border-primary': colors.border.primary,
    '--border-secondary': colors.border.secondary,
    '--border-tertiary': colors.border.tertiary,
    
    // 阴影
    '--shadow-sm': colors.shadow.sm,
    '--shadow-md': colors.shadow.md,
    '--shadow-lg': colors.shadow.lg,
    
    // 渐变
    '--gradient-primary': colors.gradient.primary,
    '--gradient-secondary': colors.gradient.secondary,
    '--gradient-hero': colors.gradient.hero,
  };
};
