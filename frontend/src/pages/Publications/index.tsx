import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Typography, Tag, Progress, Space, Tabs, Spin, Alert, message, Input, Select, Modal, Button, Tooltip } from 'antd';
import {
  ProjectOutlined,
  TeamOutlined,
  DollarOutlined,
  CalendarOutlined,
  LoadingOutlined,
  SearchOutlined,
  FileTextOutlined,
  CopyOutlined,
  FilterOutlined
} from '@ant-design/icons';

import type { PaperInfo } from '../../types';
import PaperInfoService from '../../api/paperInfo';

const { Title, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;

const Projects: React.FC = () => {
  // 状态管理
  const [papers, setPapers] = useState<PaperInfo[]>([]);
  const [filteredPapers, setFilteredPapers] = useState<PaperInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<number | undefined>(undefined);
  const [availableYears, setAvailableYears] = useState<number[]>([]);

  // 弹窗状态
  const [bibtexModalVisible, setBibtexModalVisible] = useState<boolean>(false);
  const [selectedPaper, setSelectedPaper] = useState<PaperInfo | null>(null);

  // 获取论文数据
  useEffect(() => {
    const fetchPapers = async () => {
      try {
        setLoading(true);
        setError(null);

        // 并行获取论文数据和可用年份
        const [papersData, yearsData] = await Promise.all([
          PaperInfoService.getAllPaperInfos({
            pageSize: 1000, // 获取所有论文
            sort: 'year:desc,createdAt:desc'
          }),
          PaperInfoService.getAvailableYears()
        ]);

        setPapers(papersData);
        setFilteredPapers(papersData);
        setAvailableYears(yearsData);

        console.log('获取到的论文数据:', papersData);
        console.log('可用年份:', yearsData);
      } catch (error) {
        console.error('获取论文数据失败:', error);
        setError('获取论文数据失败，请稍后重试');
        message.error('获取论文数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchPapers();
  }, []);

  // 过滤论文数据
  useEffect(() => {
    let filtered = papers;

    // 按搜索关键词过滤
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(paper =>
        paper.title?.toLowerCase().includes(term) ||
        paper.authors?.toLowerCase().includes(term) ||
        paper.journal?.toLowerCase().includes(term) ||
        paper.abstract?.toLowerCase().includes(term)
      );
    }

    // 按年份过滤
    if (selectedYear) {
      filtered = filtered.filter(paper => paper.year === selectedYear);
    }

    setFilteredPapers(filtered);
  }, [papers, searchTerm, selectedYear]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  // 处理年份选择
  const handleYearChange = (year: number | undefined) => {
    setSelectedYear(year);
  };

  // 清除所有过滤条件
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedYear(undefined);
  };

  // 显示BibTeX弹窗
  const showBibtexModal = (paper: PaperInfo) => {
    setSelectedPaper(paper);
    setBibtexModalVisible(true);
  };

  // 关闭BibTeX弹窗
  const closeBibtexModal = () => {
    setBibtexModalVisible(false);
    setSelectedPaper(null);
  };

  // 复制BibTeX到剪贴板
  const copyBibtex = async () => {
    if (selectedPaper?.bibtex) {
      try {
        await navigator.clipboard.writeText(selectedPaper.bibtex);
        message.success('BibTeX已复制到剪贴板');
      } catch (error) {
        console.error('复制失败:', error);
        message.error('复制失败，请手动复制');
      }
    }
  };

  // 渲染单个论文条目
  const renderPaperItem = (paper: PaperInfo) => {
    // 格式化显示文本：authors(year). title. journal
    const authorsText = paper.authors || '未知作者';
    const yearText = paper.year ? `(${paper.year})` : '';
    const titleText = paper.title || '无标题';
    const journalText = paper.journal || '未知期刊';

    return (
      <div
        key={paper.id}
        style={{
          padding: '16px 20px',
          borderBottom: '1px solid var(--border-secondary)',
          transition: 'background-color 0.2s ease',
          cursor: 'default'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
      >
        <div style={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: '12px',
          marginBottom: '8px'
        }}>
          {/* 论文图标 */}
          <FileTextOutlined style={{
            fontSize: '16px',
            color: 'var(--color-primary)',
            marginTop: '2px',
            flexShrink: 0
          }} />

          {/* 论文信息 */}
          <div style={{ flex: 1, lineHeight: '1.6' }}>
            <span style={{
              color: 'var(--color-primary)',
              fontWeight: '500',
              fontSize: '15px'
            }}>
              {authorsText}
            </span>
            <span style={{
              color: 'var(--color-secondary)',
              fontWeight: '600',
              fontSize: '15px',
              marginLeft: '4px'
            }}>
              {yearText}
            </span>
            <span style={{ color: 'var(--text-primary)', margin: '0 4px' }}>.</span>
            <span style={{
              color: 'var(--color-warning)',
              fontWeight: '500',
              fontSize: '15px'
            }}>
              {titleText}
            </span>
            <span style={{ color: 'var(--text-primary)', margin: '0 4px' }}>.</span>
            <span style={{
              color: 'var(--color-success)',
              fontStyle: 'italic',
              fontSize: '15px'
            }}>
              {journalText}
            </span>
          </div>

          {/* 引用按钮 */}
          {paper.bibtex && (
            <Tooltip title="查看引用格式">
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => showBibtexModal(paper)}
                style={{
                  color: 'var(--text-secondary)',
                  border: 'none',
                  boxShadow: 'none'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = 'var(--color-primary)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = 'var(--text-secondary)';
                }}
              />
            </Tooltip>
          )}
        </div>

        {/* DOI链接 */}
        {paper.doi && (
          <div style={{
            marginLeft: '28px',
            fontSize: '13px',
            color: 'var(--text-secondary)'
          }}>
            DOI:
            <a
              href={`https://doi.org/${paper.doi}`}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                color: 'var(--color-primary)',
                textDecoration: 'none',
                marginLeft: '4px'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.textDecoration = 'underline';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.textDecoration = 'none';
              }}
            >
              {paper.doi}
            </a>
          </div>
        )}
      </div>
    );
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="apple-fade-in" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh',
        flexDirection: 'column'
      }}>
        <Spin
          size="large"
          indicator={<LoadingOutlined style={{ fontSize: 48, color: 'var(--color-primary)' }} spin />}
        />
        <Paragraph style={{
          marginTop: '24px',
          color: 'var(--text-secondary)',
          fontSize: '16px'
        }}>
          正在加载论文信息...
        </Paragraph>
      </div>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className="apple-fade-in" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh'
      }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <button
              onClick={() => window.location.reload()}
              style={{
                background: 'var(--color-primary)',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '8px 16px',
                cursor: 'pointer'
              }}
            >
              重新加载
            </button>
          }
        />
      </div>
    );
  }

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      {/* 页面标题 */}
      <div style={{ textAlign: 'center', marginBottom: '48px' }}>
        <Title level={1} style={{
          margin: 0,
          color: 'var(--text-primary)',
          fontWeight: '700',
          fontSize: '3rem',
          letterSpacing: '-0.02em'
        }}>
          学术成果
        </Title>
        <Paragraph style={{
          fontSize: '20px',
          color: 'var(--text-secondary)',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '700px',
          margin: '20px auto 0'
        }}>
          我们课题组在人工智能领域发表了多篇高质量学术论文，取得了丰硕的研究成果
        </Paragraph>
      </div>

      {/* 搜索和过滤区域 */}
      <div style={{
        marginBottom: '32px',
        padding: '24px',
        background: 'var(--gradient-secondary)',
        borderRadius: '16px',
        boxShadow: 'var(--shadow-md)'
      }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={10}>
            <Search
              placeholder="搜索论文标题、作者、期刊..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="选择年份"
              allowClear
              size="large"
              value={selectedYear}
              onChange={handleYearChange}
              style={{ width: '100%' }}
              suffixIcon={<FilterOutlined />}
            >
              {availableYears.map(year => (
                <Option key={year} value={year}>{year}年</Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={4} md={8}>
            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
              gap: '12px'
            }}>
              {(searchTerm || selectedYear) && (
                <Button
                  type="link"
                  onClick={clearFilters}
                  style={{ padding: 0, height: 'auto' }}
                >
                  清除筛选
                </Button>
              )}
              <span style={{
                color: 'var(--text-secondary)',
                fontSize: '14px'
              }}>
                共 {filteredPapers.length} 篇论文
              </span>
            </div>
          </Col>
        </Row>
      </div>

      {/* 论文列表 */}
      <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
        <div style={{ width: '100%' }}>
          {filteredPapers.length > 0 ? (
            <Card
              style={{
                borderRadius: '16px',
                border: 'none',
                background: 'var(--gradient-secondary)',
                boxShadow: 'var(--shadow-md)'
              }}
              bodyStyle={{ padding: 0 }}
            >
              {filteredPapers.map(renderPaperItem)}
            </Card>
          ) : (
            <div style={{
              textAlign: 'center',
              padding: '64px 32px',
              color: 'var(--text-secondary)'
            }}>
              <FileTextOutlined style={{
                fontSize: '48px',
                color: 'var(--text-tertiary)',
                marginBottom: '16px'
              }} />
              <Title level={4} style={{ color: 'var(--text-secondary)', margin: '0 0 8px 0' }}>
                暂无论文数据
              </Title>
              <Paragraph style={{ color: 'var(--text-secondary)', margin: 0 }}>
                {searchTerm || selectedYear ? '没有找到符合条件的论文' : '暂时还没有论文数据'}
              </Paragraph>
            </div>
          )}
        </div>
      </div>

      {/* 论文统计 */}
      <div style={{ marginTop: '64px', width: '100%' }}>
        <Row gutter={[24, 24]} justify="center">
          {[
            {
              title: '论文总数',
              value: papers.length,
              color: 'var(--color-primary)',
              icon: <FileTextOutlined />
            },
            {
              title: '最新年份',
              value: availableYears.length > 0 ? availableYears[0] : '-',
              color: 'var(--color-success)',
              icon: <CalendarOutlined />
            },
            {
              title: '年份跨度',
              value: availableYears.length > 1 ?
                `${availableYears[availableYears.length - 1]}-${availableYears[0]}` :
                (availableYears.length === 1 ? availableYears[0] : '-'),
              color: 'var(--color-warning)',
              icon: <ProjectOutlined />
            }
          ].map((stat, index) => (
            <Col xs={24} sm={8} key={index}>
              <Card
                style={{
                  borderRadius: '20px',
                  border: 'none',
                  background: 'var(--gradient-secondary)',
                  boxShadow: 'var(--shadow-md)',
                  textAlign: 'center'
                }}
                bodyStyle={{ padding: '32px' }}
              >
                <div style={{
                  fontSize: '24px',
                  color: stat.color,
                  marginBottom: '12px'
                }}>
                  {stat.icon}
                </div>
                <Title level={2} style={{ color: stat.color, margin: '0 0 8px 0' }}>
                  {stat.value}
                </Title>
                <Paragraph style={{ margin: 0, color: 'var(--text-secondary)', fontSize: '16px' }}>
                  {stat.title}
                </Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* BibTeX 弹窗 */}
      <Modal
        title={
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <CopyOutlined style={{ color: 'var(--color-primary)' }} />
            <span>引用格式 (BibTeX)</span>
          </div>
        }
        open={bibtexModalVisible}
        onCancel={closeBibtexModal}
        footer={[
          <Button key="copy" type="primary" icon={<CopyOutlined />} onClick={copyBibtex}>
            复制到剪贴板
          </Button>,
          <Button key="close" onClick={closeBibtexModal}>
            关闭
          </Button>
        ]}
        width={700}
        centered
      >
        {selectedPaper && (
          <div>
            <div style={{
              marginBottom: '16px',
              padding: '12px',
              background: 'var(--bg-tertiary)',
              borderRadius: '8px',
              fontSize: '14px',
              color: 'var(--text-secondary)'
            }}>
              <strong>论文标题：</strong>{selectedPaper.title || '无标题'}
            </div>
            <div style={{
              background: 'var(--bg-tertiary)',
              border: '1px solid var(--border-primary)',
              borderRadius: '8px',
              padding: '16px',
              fontFamily: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
              fontSize: '13px',
              lineHeight: '1.5',
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-all',
              maxHeight: '400px',
              overflowY: 'auto',
              color: 'var(--text-primary)'
            }}>
              {selectedPaper.bibtex || '暂无BibTeX信息'}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Projects; 