import React from 'react';
import { Card, Button, Typography, Space, Tag, Divider } from 'antd';
import { SunOutlined, MoonOutlined, BulbOutlined } from '@ant-design/icons';
import { useTheme } from '../../contexts/theme';

const { Title, Paragraph, Text } = Typography;

const ThemeTest: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ textAlign: 'center', marginBottom: '48px' }}>
        <Title level={1} style={{ color: 'var(--text-primary)' }}>
          主题测试页面
        </Title>
        <Paragraph style={{ color: 'var(--text-secondary)', fontSize: '18px' }}>
          当前主题: {theme.mode === 'dark' ? '暗黑模式' : '浅色模式'}
        </Paragraph>
        <Button 
          type="primary" 
          size="large"
          icon={theme.mode === 'dark' ? <SunOutlined /> : <MoonOutlined />}
          onClick={toggleTheme}
          style={{
            background: 'var(--color-primary)',
            borderColor: 'var(--color-primary)'
          }}
        >
          切换到{theme.mode === 'dark' ? '浅色' : '暗黑'}主题
        </Button>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '24px' }}>
        {/* 颜色测试卡片 */}
        <Card 
          title="颜色系统测试" 
          style={{ 
            background: 'var(--bg-card)',
            borderColor: 'var(--border-primary)'
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong style={{ color: 'var(--text-primary)' }}>主要文本颜色</Text>
              <br />
              <Text style={{ color: 'var(--text-secondary)' }}>次要文本颜色</Text>
              <br />
              <Text style={{ color: 'var(--text-tertiary)' }}>第三级文本颜色</Text>
            </div>
            
            <Divider />
            
            <Space wrap>
              <Tag color="var(--color-primary)">主色</Tag>
              <Tag color="var(--color-success)">成功</Tag>
              <Tag color="var(--color-warning)">警告</Tag>
              <Tag color="var(--color-error)">错误</Tag>
            </Space>
          </Space>
        </Card>

        {/* 背景测试卡片 */}
        <Card 
          title="背景系统测试"
          style={{ 
            background: 'var(--bg-card)',
            borderColor: 'var(--border-primary)'
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ 
              padding: '16px', 
              background: 'var(--bg-primary)', 
              borderRadius: '8px',
              border: '1px solid var(--border-secondary)'
            }}>
              主背景色
            </div>
            <div style={{ 
              padding: '16px', 
              background: 'var(--bg-secondary)', 
              borderRadius: '8px',
              border: '1px solid var(--border-secondary)'
            }}>
              次要背景色
            </div>
            <div style={{ 
              padding: '16px', 
              background: 'var(--bg-tertiary)', 
              borderRadius: '8px',
              border: '1px solid var(--border-secondary)'
            }}>
              第三级背景色
            </div>
          </Space>
        </Card>

        {/* 阴影测试卡片 */}
        <Card 
          title="阴影系统测试"
          style={{ 
            background: 'var(--bg-card)',
            borderColor: 'var(--border-primary)'
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ 
              padding: '16px', 
              background: 'var(--bg-secondary)', 
              borderRadius: '8px',
              boxShadow: 'var(--shadow-sm)'
            }}>
              小阴影
            </div>
            <div style={{ 
              padding: '16px', 
              background: 'var(--bg-secondary)', 
              borderRadius: '8px',
              boxShadow: 'var(--shadow-md)'
            }}>
              中等阴影
            </div>
            <div style={{ 
              padding: '16px', 
              background: 'var(--bg-secondary)', 
              borderRadius: '8px',
              boxShadow: 'var(--shadow-lg)'
            }}>
              大阴影
            </div>
          </Space>
        </Card>

        {/* 渐变测试卡片 */}
        <Card 
          title="渐变系统测试"
          style={{ 
            background: 'var(--bg-card)',
            borderColor: 'var(--border-primary)'
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ 
              padding: '24px', 
              background: 'var(--gradient-primary)', 
              borderRadius: '12px',
              color: 'white',
              textAlign: 'center'
            }}>
              主渐变
            </div>
            <div style={{ 
              padding: '24px', 
              background: 'var(--gradient-secondary)', 
              borderRadius: '12px',
              color: 'var(--text-primary)',
              textAlign: 'center',
              border: '1px solid var(--border-primary)'
            }}>
              次要渐变
            </div>
          </Space>
        </Card>

        {/* 组件测试卡片 */}
        <Card 
          title="组件测试"
          style={{ 
            background: 'var(--bg-card)',
            borderColor: 'var(--border-primary)'
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button type="primary" icon={<BulbOutlined />}>
              主要按钮
            </Button>
            <Button type="default">
              默认按钮
            </Button>
            <Button type="dashed">
              虚线按钮
            </Button>
            <Button type="text">
              文本按钮
            </Button>
          </Space>
        </Card>

        {/* 边框测试卡片 */}
        <Card 
          title="边框系统测试"
          style={{ 
            background: 'var(--bg-card)',
            borderColor: 'var(--border-primary)'
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ 
              padding: '16px', 
              background: 'var(--bg-secondary)', 
              borderRadius: '8px',
              border: '1px solid var(--border-primary)'
            }}>
              主边框
            </div>
            <div style={{ 
              padding: '16px', 
              background: 'var(--bg-secondary)', 
              borderRadius: '8px',
              border: '1px solid var(--border-secondary)'
            }}>
              次要边框
            </div>
            <div style={{ 
              padding: '16px', 
              background: 'var(--bg-secondary)', 
              borderRadius: '8px',
              border: '1px solid var(--border-tertiary)'
            }}>
              第三级边框
            </div>
          </Space>
        </Card>
      </div>
    </div>
  );
};

export default ThemeTest;
