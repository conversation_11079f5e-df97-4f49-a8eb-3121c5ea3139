import React from 'react';
import { Card, Row, Col, Typography, Space, Divider, Button } from 'antd';
import {
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  ClockCircleOutlined,
  UserOutlined,
  GlobalOutlined,
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const Contact: React.FC = () => {
  const contactInfo = {
    address: '北京市海淀区清华大学计算机科学与技术系',
    phone: '010-12345678',
    email: '<EMAIL>',
    website: 'https://research.university.edu.cn',
    workingHours: '周一至周五 9:00-18:00',
    emergencyContact: '李教授: 138-1234-5678'
  };

  const teamLeaders = [
    {
      name: '李教授',
      title: '课题组负责人',
      email: '<EMAIL>',
      phone: '010-12345678',
      office: '清华大学主楼 8层 801室',
      researchAreas: ['人工智能', '机器学习', '计算机视觉']
    }
  ];

  const directions = [
    {
      title: '公共交通',
      description: '乘坐地铁4号线到清华大学站下车，从东门进入校园，步行约10分钟到达计算机系。',
      details: [
        '地铁4号线：清华大学站',
        '公交：375路、549路、特8路',
        '步行：从东门到计算机系约10分钟'
      ]
    },
    {
      title: '自驾车',
      description: '校园内停车位有限，建议提前联系预约停车。',
      details: [
        '导航地址：清华大学计算机科学与技术系',
        '停车地点：主楼地下停车场',
        '注意事项：需要提前预约停车位'
      ]
    }
  ];

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      <div style={{ textAlign: 'center', marginBottom: '64px' }}>
        <Title level={1} style={{
          margin: 0,
          color: '#1d1d1f',
          fontWeight: '700',
          fontSize: '3rem',
          letterSpacing: '-0.02em'
        }}>
          联系我们
        </Title>
        <Paragraph style={{
          fontSize: '20px',
          color: '#86868b',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '700px',
          margin: '20px auto 0'
        }}>
          欢迎与我们联系，我们期待与您进行学术交流和合作
        </Paragraph>
      </div>

      <div style={{ width: '100%', maxWidth: '1200px', margin: '0 auto' }}>
        {/* 联系信息 */}
        <Card
          title="联系信息"
          style={{
            borderRadius: '20px',
            border: 'none',
            background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            marginBottom: '32px'
          }}
          bodyStyle={{ padding: '32px' }}
        >
          <Row gutter={[24, 24]}>
            <Col xs={24} md={12}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div>
                <EnvironmentOutlined style={{ fontSize: 20, color: '#1890ff', marginRight: 8 }} />
                <strong>地址：</strong>
                <Paragraph style={{ margin: '8px 0 0 20px' }}>
                  {contactInfo.address}
                </Paragraph>
              </div>
              
              <div>
                <PhoneOutlined style={{ fontSize: 20, color: '#52c41a', marginRight: 8 }} />
                <strong>电话：</strong>
                <Paragraph style={{ margin: '8px 0 0 20px' }}>
                  {contactInfo.phone}
                </Paragraph>
              </div>
              
              <div>
                <MailOutlined style={{ fontSize: 20, color: '#faad14', marginRight: 8 }} />
                <strong>邮箱：</strong>
                <Paragraph style={{ margin: '8px 0 0 20px' }}>
                  <a href={`mailto:${contactInfo.email}`}>{contactInfo.email}</a>
                </Paragraph>
              </div>
            </Space>
          </Col>
          
          <Col xs={24} md={12}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div>
                <GlobalOutlined style={{ fontSize: 20, color: '#722ed1', marginRight: 8 }} />
                <strong>网站：</strong>
                <Paragraph style={{ margin: '8px 0 0 20px' }}>
                  <a href={contactInfo.website} target="_blank" rel="noopener noreferrer">
                    {contactInfo.website}
                  </a>
                </Paragraph>
              </div>
              
              <div>
                <ClockCircleOutlined style={{ fontSize: 20, color: '#f5222d', marginRight: 8 }} />
                <strong>工作时间：</strong>
                <Paragraph style={{ margin: '8px 0 0 20px' }}>
                  {contactInfo.workingHours}
                </Paragraph>
              </div>
              
              <div>
                <UserOutlined style={{ fontSize: 20, color: '#13c2c2', marginRight: 8 }} />
                <strong>紧急联系：</strong>
                <Paragraph style={{ margin: '8px 0 0 20px' }}>
                  {contactInfo.emergencyContact}
                </Paragraph>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 课题组负责人 */}
      <Card title="课题组负责人" className="content-card" style={{ marginTop: 24 }}>
        <Row gutter={[24, 24]}>
          {teamLeaders.map((leader, index) => (
            <Col xs={24} md={12} key={index}>
              <Card size="small" hoverable>
                <div style={{ textAlign: 'center', marginBottom: 16 }}>
                  <div style={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    backgroundColor: '#1890ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 12px',
                    color: 'white',
                    fontSize: 24,
                    fontWeight: 'bold'
                  }}>
                    {leader.name.charAt(0)}
                  </div>
                  <Title level={4} style={{ margin: '8px 0' }}>
                    {leader.name}
                  </Title>
                  <Paragraph style={{ color: '#666', margin: '4px 0' }}>
                    {leader.title}
                  </Paragraph>
                </div>
                
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <MailOutlined style={{ marginRight: 8 }} />
                    <a href={`mailto:${leader.email}`}>{leader.email}</a>
                  </div>
                  <div>
                    <PhoneOutlined style={{ marginRight: 8 }} />
                    {leader.phone}
                  </div>
                  <div>
                    <EnvironmentOutlined style={{ marginRight: 8 }} />
                    {leader.office}
                  </div>
                  <div>
                    <strong>研究方向：</strong>
                    <div style={{ marginTop: 4 }}>
                      {leader.researchAreas.map((area, idx) => (
                        <span key={idx} style={{
                          display: 'inline-block',
                          backgroundColor: '#f0f0f0',
                          padding: '2px 8px',
                          borderRadius: 4,
                          margin: '2px',
                          fontSize: 12
                        }}>
                          {area}
                        </span>
                      ))}
                    </div>
                  </div>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 交通指南 */}
      <Card title="交通指南" className="content-card" style={{ marginTop: 24 }}>
        <Row gutter={[24, 24]}>
          {directions.map((direction, index) => (
            <Col xs={24} md={12} key={index}>
              <Card size="small" title={direction.title}>
                <Paragraph style={{ marginBottom: 16 }}>
                  {direction.description}
                </Paragraph>
                <ul style={{ paddingLeft: 20 }}>
                  {direction.details.map((detail, idx) => (
                    <li key={idx}>{detail}</li>
                  ))}
                </ul>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 地图占位符 */}
      <Card title="位置地图" className="content-card" style={{ marginTop: 24 }}>
        <div style={{
          height: 400,
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: 8,
          border: '2px dashed #d9d9d9'
        }}>
          <div style={{ textAlign: 'center' }}>
            <EnvironmentOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
            <Paragraph style={{ color: '#999', margin: 0 }}>
              地图组件将在这里显示<br />
              可以集成百度地图、高德地图或Google Maps
            </Paragraph>
          </div>
        </div>
      </Card>

      {/* 联系表单 */}
      <Card title="在线留言" className="content-card" style={{ marginTop: 24 }}>
        <Paragraph style={{ textAlign: 'center', color: '#666' }}>
          如果您有任何问题或建议，欢迎通过以下方式联系我们：
        </Paragraph>
        <Row gutter={[16, 16]} style={{ textAlign: 'center' }}>
          <Col xs={24} sm={8}>
            <Button type="primary" size="large" icon={<MailOutlined />}>
              发送邮件
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button size="large" icon={<PhoneOutlined />}>
              电话咨询
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button size="large" icon={<UserOutlined />}>
              预约访问
            </Button>
          </Col>
        </Row>
      </Card>
      </div>
    </div>
  );
};

export default Contact; 