import React, { useState } from 'react';
import { Card, Row, Col, Typography, Tag, Space, Image, Badge, Button, Modal } from 'antd';
import {
  ExperimentOutlined,
  BulbOutlined,
  RocketOutlined,
  EyeOutlined,
  StarOutlined,
  TrophyOutlined,
  ToolOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const Research: React.FC = () => {
  const [selectedDirection, setSelectedDirection] = useState<any>(null);
  const [modalVisible, setModalVisible] = useState(false);

  const researchDirections = [
    {
      id: 1,
      title: '计算机视觉',
      description: '计算机视觉是人工智能的重要分支，致力于让计算机能够理解和处理视觉信息。我们的研究包括图像识别、目标检测、视频分析、人脸识别等领域。',
      keywords: ['图像识别', '目标检测', '视频分析', '人脸识别', '深度学习'],
      image: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=600&h=400&fit=crop',
      color: '#007aff',
      icon: <EyeOutlined />,
      applications: [
        '智能监控系统',
        '自动驾驶技术',
        '医疗影像诊断',
        '工业质量检测',
        '增强现实应用'
      ],
      achievements: [
        '在ImageNet竞赛中取得优异成绩',
        '发表多篇CVPR、ICCV顶级会议论文',
        '获得多项相关专利授权'
      ]
    },
    {
      id: 2,
      title: '自然语言处理',
      description: '自然语言处理研究如何让计算机理解、生成和处理人类语言。我们专注于机器翻译、文本分类、情感分析、对话系统等方向。',
      keywords: ['机器翻译', '文本分类', '情感分析', '对话系统', '预训练模型'],
      image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop',
      color: '#34c759',
      icon: <BulbOutlined />,
      applications: [
        '智能客服系统',
        '多语言翻译工具',
        '舆情分析平台',
        '智能写作助手',
        '语音交互系统'
      ],
      achievements: [
        '开发了高精度的中文文本分类系统',
        '在机器翻译任务上达到国际先进水平',
        '获得多项NLP相关项目资助'
      ]
    },
    {
      id: 3,
      title: '强化学习',
      description: '强化学习通过智能体与环境的交互来学习最优策略。我们研究深度强化学习算法及其在游戏AI、机器人控制、推荐系统等领域的应用。',
      keywords: ['深度强化学习', '游戏AI', '机器人控制', '推荐系统', '策略优化'],
      image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=600&h=400&fit=crop',
      color: '#ff9500',
      icon: <RocketOutlined />,
      applications: [
        '游戏AI开发',
        '机器人路径规划',
        '智能推荐算法',
        '资源调度优化',
        '自动驾驶决策'
      ],
      achievements: [
        '在多个游戏环境中实现了超人类水平的AI',
        '开发了高效的机器人控制算法',
        '在推荐系统优化方面取得重要突破'
      ]
    },
    {
      id: 4,
      title: '数据挖掘与机器学习',
      description: '数据挖掘从大规模数据中发现有价值的知识和模式。我们结合传统机器学习和深度学习技术，解决实际应用中的数据分析问题。',
      keywords: ['数据挖掘', '机器学习', '大数据分析', '模式识别', '预测建模'],
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop',
      color: '#ff3b30',
      icon: <StarOutlined />,
      applications: [
        '商业智能分析',
        '金融风险预测',
        '医疗健康数据分析',
        '社交网络分析',
        '物联网数据处理'
      ],
      achievements: [
        '开发了多个行业数据分析解决方案',
        '在多个数据挖掘竞赛中获得优异成绩',
        '发表了多篇相关领域的高质量论文'
      ]
    },
    {
      id: 5,
      title: '边缘计算与物联网',
      description: '边缘计算将计算能力部署在数据源附近，实现低延迟、高效率的数据处理。我们研究边缘智能算法和物联网应用。',
      keywords: ['边缘计算', '物联网', '智能感知', '实时处理', '分布式系统'],
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop',
      color: '#5856d6',
      icon: <ToolOutlined />,
      applications: [
        '智能家居系统',
        '工业物联网',
        '智慧城市',
        '移动边缘计算',
        '5G网络优化'
      ],
      achievements: [
        '开发了多个边缘计算原型系统',
        '在物联网安全方面取得重要进展',
        '获得多项相关技术专利'
      ]
    }
  ];

  const handleDirectionClick = (direction: any) => {
    setSelectedDirection(direction);
    setModalVisible(true);
  };

  return (
    <div className="apple-fade-in">
      {/* 页面标题 */}
      <div style={{ textAlign: 'center', marginBottom: '64px' }}>
        <Badge
          count={<ExperimentOutlined style={{ color: '#007aff' }} />}
          style={{ backgroundColor: 'transparent' }}
        >
          <Title level={1} style={{
            margin: 0,
            color: '#1d1d1f',
            fontWeight: '700',
            fontSize: '3rem',
            letterSpacing: '-0.02em'
          }}>
            研究方向
          </Title>
        </Badge>
        <Paragraph style={{
          fontSize: '20px',
          color: '#86868b',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '700px',
          margin: '20px auto 0'
        }}>
          我们课题组在人工智能的多个前沿领域开展深入研究，致力于解决实际应用中的关键问题
        </Paragraph>
      </div>

      {/* 研究方向网格 */}
      <div className="apple-grid" style={{ gap: '32px', marginBottom: '80px', width: '100%' }}>
        {researchDirections.map((direction) => (
          <Card
            key={direction.id}
            style={{
              borderRadius: '24px',
              border: 'none',
              background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
              transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
              cursor: 'pointer',
              overflow: 'hidden',
              height: '100%'
            }}
            hoverable
            bodyStyle={{ padding: 0 }}
            onClick={() => handleDirectionClick(direction)}
          >
            {/* 图片区域 */}
            <div style={{ position: 'relative', overflow: 'hidden' }}>
              <Image
                src={direction.image}
                alt={direction.title}
                style={{
                  width: '100%',
                  height: '240px',
                  objectFit: 'cover'
                }}
                preview={false}
              />
              <div style={{
                position: 'absolute',
                top: '20px',
                left: '20px',
                width: '48px',
                height: '48px',
                borderRadius: '12px',
                background: `${direction.color}20`,
                backdropFilter: 'blur(10px)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '20px',
                color: direction.color
              }}>
                {direction.icon}
              </div>
            </div>

            {/* 内容区域 */}
            <div style={{ padding: '32px' }}>
              <Title level={3} style={{
                margin: '0 0 16px 0',
                color: '#1d1d1f',
                fontWeight: '600',
                fontSize: '1.5rem'
              }}>
                {direction.title}
              </Title>

              <Paragraph style={{
                fontSize: '15px',
                color: '#86868b',
                lineHeight: '1.6',
                marginBottom: '20px',
                display: '-webkit-box',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}>
                {direction.description}
              </Paragraph>

              {/* 关键词标签 */}
              <div style={{ marginBottom: '20px' }}>
                <Space wrap size={[8, 8]}>
                  {direction.keywords.slice(0, 3).map((keyword, index) => (
                    <Tag
                      key={index}
                      style={{
                        background: `${direction.color}15`,
                        color: direction.color,
                        border: `1px solid ${direction.color}30`,
                        borderRadius: '20px',
                        padding: '4px 12px',
                        fontSize: '12px',
                        fontWeight: '500'
                      }}
                    >
                      {keyword}
                    </Tag>
                  ))}
                  {direction.keywords.length > 3 && (
                    <Tag
                      style={{
                        background: '#f0f0f0',
                        color: '#86868b',
                        border: '1px solid #e0e0e0',
                        borderRadius: '20px',
                        padding: '4px 12px',
                        fontSize: '12px'
                      }}
                    >
                      +{direction.keywords.length - 3}
                    </Tag>
                  )}
                </Space>
              </div>

              {/* 统计信息 */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: '20px',
                borderTop: '1px solid #f0f0f0'
              }}>
                <div>
                  <div style={{
                    fontSize: '14px',
                    color: '#86868b',
                    marginBottom: '4px'
                  }}>
                    应用领域
                  </div>
                  <div style={{
                    fontSize: '18px',
                    fontWeight: '600',
                    color: '#1d1d1f'
                  }}>
                    {direction.applications.length}+
                  </div>
                </div>
                <Button
                  type="text"
                  icon={<ArrowRightOutlined />}
                  style={{
                    color: direction.color,
                    fontSize: '16px'
                  }}
                >
                  了解更多
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* 研究设施 */}
      <div style={{ marginBottom: '80px' }}>
        <div style={{
          textAlign: 'center',
          marginBottom: '48px'
        }}>
          <Badge
            count={<ToolOutlined style={{ color: '#5856d6' }} />}
            style={{ backgroundColor: 'transparent' }}
          >
            <Title level={2} style={{
              margin: 0,
              color: '#1d1d1f',
              fontWeight: '700',
              fontSize: '2.5rem',
              letterSpacing: '-0.02em'
            }}>
              研究设施
            </Title>
          </Badge>
          <Paragraph style={{
            fontSize: '18px',
            color: '#86868b',
            marginTop: '16px',
            marginBottom: 0
          }}>
            先进的实验设备为我们的研究提供强有力的支撑
          </Paragraph>
        </div>

        <div className="apple-grid-3" style={{ width: '100%' }}>
          {[
            {
              title: '高性能计算集群',
              description: '配备多台GPU服务器，支持大规模深度学习模型训练和推理，提供强大的计算能力支持。',
              icon: <RocketOutlined />,
              color: '#007aff',
              specs: ['NVIDIA A100 GPU × 8', '512GB 内存', '100TB 存储']
            },
            {
              title: '数据采集设备',
              description: '包括各种传感器、摄像头、音频设备等，用于多模态数据采集和实验验证。',
              icon: <EyeOutlined />,
              color: '#34c759',
              specs: ['高清摄像头', '多种传感器', '音频采集设备']
            },
            {
              title: '实验平台',
              description: '机器人平台、无人机、智能小车等，用于算法验证和实际应用测试。',
              icon: <ToolOutlined />,
              color: '#ff9500',
              specs: ['机器人平台', '无人机系统', '智能小车']
            }
          ].map((facility, index) => (
            <Card
              key={index}
              style={{
                borderRadius: '20px',
                border: 'none',
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                height: '100%'
              }}
              hoverable
              bodyStyle={{ padding: '32px' }}
            >
              <div style={{ textAlign: 'center', marginBottom: '24px' }}>
                <div style={{
                  width: '64px',
                  height: '64px',
                  borderRadius: '16px',
                  background: `linear-gradient(135deg, ${facility.color}20, ${facility.color}10)`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 16px',
                  fontSize: '24px',
                  color: facility.color
                }}>
                  {facility.icon}
                </div>
                <Title level={4} style={{
                  margin: '0 0 12px 0',
                  color: '#1d1d1f',
                  fontWeight: '600'
                }}>
                  {facility.title}
                </Title>
              </div>

              <Paragraph style={{
                fontSize: '14px',
                color: '#86868b',
                lineHeight: '1.6',
                marginBottom: '20px',
                textAlign: 'center'
              }}>
                {facility.description}
              </Paragraph>

              <div style={{
                borderTop: '1px solid #f0f0f0',
                paddingTop: '20px'
              }}>
                <div style={{
                  fontSize: '14px',
                  fontWeight: '600',
                  color: '#1d1d1f',
                  marginBottom: '12px'
                }}>
                  主要配置
                </div>
                <Space direction="vertical" style={{ width: '100%' }} size={8}>
                  {facility.specs.map((spec, specIndex) => (
                    <div key={specIndex} style={{
                      fontSize: '13px',
                      color: '#86868b',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <div style={{
                        width: '4px',
                        height: '4px',
                        borderRadius: '50%',
                        background: facility.color,
                        marginRight: '8px'
                      }} />
                      {spec}
                    </div>
                  ))}
                </Space>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* 研究方向详情模态框 */}
      <Modal
        title={null}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
        styles={{
          content: {
            borderRadius: '20px',
            overflow: 'hidden'
          }
        }}
      >
        {selectedDirection && (
          <div>
            {/* 头部图片 */}
            <div style={{ position: 'relative', marginBottom: '32px' }}>
              <Image
                src={selectedDirection.image}
                alt={selectedDirection.title}
                style={{
                  width: '100%',
                  height: '300px',
                  objectFit: 'cover'
                }}
                preview={false}
              />
              <div style={{
                position: 'absolute',
                bottom: '24px',
                left: '24px',
                right: '24px',
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
                borderRadius: '16px',
                padding: '24px'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <div style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '12px',
                    background: `${selectedDirection.color}20`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '20px',
                    color: selectedDirection.color,
                    marginRight: '16px'
                  }}>
                    {selectedDirection.icon}
                  </div>
                  <Title level={2} style={{
                    margin: 0,
                    color: '#1d1d1f',
                    fontWeight: '600'
                  }}>
                    {selectedDirection.title}
                  </Title>
                </div>
              </div>
            </div>

            <div style={{ padding: '0 24px 24px' }}>
              {/* 详细描述 */}
              <div style={{ marginBottom: '32px' }}>
                <Paragraph style={{
                  fontSize: '16px',
                  color: '#1d1d1f',
                  lineHeight: '1.7',
                  margin: 0
                }}>
                  {selectedDirection.description}
                </Paragraph>
              </div>

              {/* 关键词 */}
              <div style={{ marginBottom: '32px' }}>
                <Title level={4} style={{
                  color: '#1d1d1f',
                  marginBottom: '16px'
                }}>
                  关键技术
                </Title>
                <Space wrap size={[12, 12]}>
                  {selectedDirection.keywords.map((keyword, index) => (
                    <Tag
                      key={index}
                      style={{
                        background: `${selectedDirection.color}15`,
                        color: selectedDirection.color,
                        border: `1px solid ${selectedDirection.color}30`,
                        borderRadius: '20px',
                        padding: '6px 16px',
                        fontSize: '14px',
                        fontWeight: '500'
                      }}
                    >
                      {keyword}
                    </Tag>
                  ))}
                </Space>
              </div>

              {/* 应用领域和主要成果 */}
              <Row gutter={[32, 32]}>
                <Col xs={24} md={12}>
                  <div>
                    <Title level={4} style={{
                      color: '#1d1d1f',
                      marginBottom: '16px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <BulbOutlined style={{ marginRight: '8px', color: '#34c759' }} />
                      应用领域
                    </Title>
                    <Space direction="vertical" style={{ width: '100%' }} size={12}>
                      {selectedDirection.applications.map((app, index) => (
                        <div key={index} style={{
                          padding: '12px 16px',
                          borderRadius: '12px',
                          background: '#f8f9fa',
                          fontSize: '14px',
                          color: '#1d1d1f'
                        }}>
                          {app}
                        </div>
                      ))}
                    </Space>
                  </div>
                </Col>
                <Col xs={24} md={12}>
                  <div>
                    <Title level={4} style={{
                      color: '#1d1d1f',
                      marginBottom: '16px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <TrophyOutlined style={{ marginRight: '8px', color: '#ff9500' }} />
                      主要成果
                    </Title>
                    <Space direction="vertical" style={{ width: '100%' }} size={12}>
                      {selectedDirection.achievements.map((achievement, index) => (
                        <div key={index} style={{
                          padding: '12px 16px',
                          borderRadius: '12px',
                          background: '#f8f9fa',
                          fontSize: '14px',
                          color: '#1d1d1f'
                        }}>
                          {achievement}
                        </div>
                      ))}
                    </Space>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Research; 