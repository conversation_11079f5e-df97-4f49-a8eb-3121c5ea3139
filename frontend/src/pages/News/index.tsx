import React from 'react';
import { Card, Row, Col, Typography, Tag, Space, List, Avatar } from 'antd';
import { FileTextOutlined, CalendarOutlined, UserOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const News: React.FC = () => {
  const newsData = [
    {
      id: 1,
      title: '课题组获得国家自然科学基金项目',
      summary: '我们课题组成功申请到国家自然科学基金面上项目，项目经费200万元，将用于深度学习在医学图像分析中的应用研究。',
      content: '近日，我们课题组成功申请到国家自然科学基金面上项目"基于深度学习的医学图像智能诊断关键技术研究"，项目经费200万元，研究周期4年。该项目将重点研究深度学习在医学图像分析中的应用，包括图像分割、病变检测、诊断辅助等关键技术。项目的研究成果将为医疗AI的发展提供重要支撑。',
      category: '项目获批',
      tags: ['国家自然科学基金', '深度学习', '医学图像'],
      author: '李教授',
      publishDate: '2024-12-15',
      cover: 'https://via.placeholder.com/300x200/1890ff/ffffff?text=项目获批',
      views: 156
    },
    {
      id: 2,
      title: '团队成员在国际顶级会议发表论文',
      summary: '课题组博士生张三的论文被CVPR 2024接收，这是计算机视觉领域的顶级会议，标志着我们在该领域的研究水平达到了国际先进水平。',
      content: '我们课题组博士生张三的论文"Efficient Object Detection in Medical Images Using Deep Learning"被计算机视觉领域顶级会议CVPR 2024接收。该论文提出了一种高效的医学图像目标检测方法，在多个公开数据集上取得了优异的性能。CVPR是计算机视觉领域最具影响力的国际会议，论文被接收标志着我们在该领域的研究水平达到了国际先进水平。',
      category: '学术成果',
      tags: ['CVPR', '论文发表', '计算机视觉'],
      author: '张三',
      publishDate: '2024-12-10',
      cover: 'https://via.placeholder.com/300x200/52c41a/ffffff?text=论文发表',
      views: 203
    },
    {
      id: 3,
      title: '举办学术交流活动',
      summary: '课题组邀请了来自MIT的知名学者进行学术报告，分享了在人工智能领域的最新研究成果，为团队成员提供了宝贵的学习机会。',
      content: '为了促进学术交流，我们课题组邀请了来自麻省理工学院（MIT）的知名学者Dr. John Smith进行学术报告。报告主题为"Recent Advances in Deep Learning for Computer Vision"，Dr. Smith分享了他们在深度学习领域的最新研究成果，包括新型网络架构、训练策略优化等方面的创新。报告吸引了来自校内外的众多师生参加，现场讨论热烈，为团队成员提供了宝贵的学习机会。',
      category: '学术交流',
      tags: ['学术报告', 'MIT', '深度学习'],
      author: '王五',
      publishDate: '2024-12-05',
      cover: 'https://via.placeholder.com/300x200/faad14/ffffff?text=学术交流',
      views: 89
    },
    {
      id: 4,
      title: '课题组参加国际人工智能大会',
      summary: '我们课题组参加了在旧金山举办的AAAI 2024国际人工智能大会，展示了多项研究成果，并与国际同行进行了深入交流。',
      content: '我们课题组派出代表参加了在美国旧金山举办的AAAI 2024国际人工智能大会。会议期间，我们展示了多项研究成果，包括多模态情感分析、强化学习算法优化等方面的最新进展。团队成员与国际同行进行了深入交流，建立了多个合作联系。AAAI是人工智能领域最重要的国际会议之一，参会为课题组提供了展示研究成果和了解国际前沿的重要平台。',
      category: '会议参会',
      tags: ['AAAI', '国际会议', '人工智能'],
      author: '李四',
      publishDate: '2024-11-28',
      cover: 'https://via.placeholder.com/300x200/f5222d/ffffff?text=国际会议',
      views: 134
    },
    {
      id: 5,
      title: '课题组获得多项专利授权',
      summary: '我们课题组申请的"一种基于深度学习的医学图像智能诊断方法"等多项专利获得国家知识产权局授权。',
      content: '近日，我们课题组申请的"一种基于深度学习的医学图像智能诊断方法"、"多模态情感分析系统"等多项发明专利获得国家知识产权局授权。这些专利涵盖了我们在人工智能领域的多项核心技术，体现了课题组在技术创新方面的实力。专利的授权为后续的技术转化和产业化应用奠定了重要基础。',
      category: '专利授权',
      tags: ['专利授权', '深度学习', '技术创新'],
      author: '赵六',
      publishDate: '2024-11-20',
      cover: 'https://via.placeholder.com/300x200/722ed1/ffffff?text=专利授权',
      views: 78
    }
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case '项目获批': return 'green';
      case '学术成果': return 'blue';
      case '学术交流': return 'orange';
      case '会议参会': return 'purple';
      case '专利授权': return 'red';
      default: return 'default';
    }
  };

  const renderNewsItem = (item: any) => (
    <List.Item key={item.id}>
      <Card hoverable style={{ width: '100%' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} md={8}>
            <img
              src={item.cover}
              alt={item.title}
              style={{ width: '100%', height: 200, objectFit: 'cover', borderRadius: 8 }}
            />
          </Col>
          <Col xs={24} md={16}>
            <div style={{ marginBottom: 12 }}>
              <Tag color={getCategoryColor(item.category)}>{item.category}</Tag>
              <Title level={4} style={{ margin: '8px 0' }}>
                {item.title}
              </Title>
            </div>
            
            <Paragraph style={{ color: '#666', marginBottom: 12 }}>
              {item.summary}
            </Paragraph>

            <Space style={{ marginBottom: 12 }}>
              <span>
                <UserOutlined style={{ marginRight: 4 }} />
                {item.author}
              </span>
              <span>
                <CalendarOutlined style={{ marginRight: 4 }} />
                {item.publishDate}
              </span>
              <span>
                <FileTextOutlined style={{ marginRight: 4 }} />
                {item.views} 次浏览
              </span>
            </Space>

            <div>
              <Space wrap>
                {item.tags.map((tag: string, index: number) => (
                  <Tag key={index} color="blue">{tag}</Tag>
                ))}
              </Space>
            </div>
          </Col>
        </Row>
      </Card>
    </List.Item>
  );

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      <div style={{ textAlign: 'center', marginBottom: '64px' }}>
        <Title level={1} style={{
          margin: 0,
          color: '#1d1d1f',
          fontWeight: '700',
          fontSize: '3rem',
          letterSpacing: '-0.02em'
        }}>
          新闻动态
        </Title>
        <Paragraph style={{
          fontSize: '20px',
          color: '#86868b',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '700px',
          margin: '20px auto 0'
        }}>
          了解课题组的最新动态、学术成果、项目进展和重要活动
        </Paragraph>
      </div>

      <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
        <List
          style={{ width: '100%', maxWidth: '1200px' }}
          dataSource={newsData}
          renderItem={renderNewsItem}
          pagination={{
            pageSize: 5,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </div>

      {/* 新闻分类统计 */}
      <Card title="新闻分类" className="content-card" style={{ marginTop: 32 }}>
        <Row gutter={[24, 24]}>
          <Col xs={12} md={6}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: '#52c41a', margin: 0 }}>1</Title>
                <Paragraph style={{ margin: 0 }}>项目获批</Paragraph>
              </div>
            </Card>
          </Col>
          <Col xs={12} md={6}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: '#1890ff', margin: 0 }}>1</Title>
                <Paragraph style={{ margin: 0 }}>学术成果</Paragraph>
              </div>
            </Card>
          </Col>
          <Col xs={12} md={6}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: '#faad14', margin: 0 }}>1</Title>
                <Paragraph style={{ margin: 0 }}>学术交流</Paragraph>
              </div>
            </Card>
          </Col>
          <Col xs={12} md={6}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: '#722ed1', margin: 0 }}>2</Title>
                <Paragraph style={{ margin: 0 }}>其他新闻</Paragraph>
              </div>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default News; 