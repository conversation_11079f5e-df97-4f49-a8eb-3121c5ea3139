/* CSS变量定义 - 默认浅色主题 */
:root {
  /* 基础颜色 */
  --color-primary: #007aff;
  --color-secondary: #5856d6;
  --color-success: #34c759;
  --color-warning: #ff9500;
  --color-error: #ff3b30;
  --color-info: #007aff;

  /* 背景颜色 */
  --bg-primary: #fbfbfd;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f2f2f7;
  --bg-elevated: #ffffff;
  --bg-card: #ffffff;

  /* 文字颜色 */
  --text-primary: #1d1d1f;
  --text-secondary: #86868b;
  --text-tertiary: #c7c7cc;
  --text-inverse: #ffffff;
  --text-disabled: #c7c7cc;

  /* 边框颜色 */
  --border-primary: rgba(0, 0, 0, 0.08);
  --border-secondary: rgba(0, 0, 0, 0.04);
  --border-tertiary: #d2d2d7;

  /* 阴影 */
  --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 40px rgba(0, 0, 0, 0.16);

  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #007aff, #5856d6);
  --gradient-secondary: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  --gradient-hero: linear-gradient(135deg, rgba(0, 122, 255, 0.8), rgba(88, 86, 214, 0.8));
}

/* 暗色主题变量 */
[data-theme="dark"] {
  /* 基础颜色 */
  --color-primary: #0a84ff;
  --color-secondary: #5e5ce6;
  --color-success: #30d158;
  --color-warning: #ff9f0a;
  --color-error: #ff453a;
  --color-info: #0a84ff;

  /* 背景颜色 */
  --bg-primary: #000000;
  --bg-secondary: #1c1c1e;
  --bg-tertiary: #2c2c2e;
  --bg-elevated: #1c1c1e;
  --bg-card: #1c1c1e;

  /* 文字颜色 */
  --text-primary: #ffffff;
  --text-secondary: #ebebf5;
  --text-tertiary: #8e8e93;
  --text-inverse: #000000;
  --text-disabled: #48484a;

  /* 边框颜色 */
  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.05);
  --border-tertiary: #38383a;

  /* 阴影 */
  --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 40px rgba(0, 0, 0, 0.5);

  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #0a84ff, #5e5ce6);
  --gradient-secondary: linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%);
  --gradient-hero: linear-gradient(135deg, rgba(10, 132, 255, 0.8), rgba(94, 92, 230, 0.8));
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  font-size: 16px;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: color 0.3s ease, background-color 0.3s ease;
}

/* 滚动条样式 - 苹果风格 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--text-tertiary);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
  background-clip: content-box;
}

/* 链接样式 */
a {
  text-decoration: none;
  color: inherit;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  width: 100%;
  box-sizing: border-box;
}

/* 页面标题样式 - 苹果风格 */
.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: var(--text-primary);
  text-align: center;
  letter-spacing: -0.02em;
  transition: color 0.3s ease;
}

.page-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 2rem;
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* 卡片样式 - 苹果风格 */
.content-card {
  background: var(--bg-card);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  padding: 32px;
  margin-bottom: 32px;
  border: 1px solid var(--border-secondary);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.content-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-title {
    font-size: 2rem !important;
    margin-bottom: 2rem !important;
  }

  .content-card {
    padding: 16px;
    margin-bottom: 16px;
  }

  /* 移动端布局调整 */
  .ant-layout-content > div {
    padding: 24px 16px !important;
  }

  .ant-carousel {
    margin: 0 -16px;
  }

  .apple-grid,
  .apple-grid-2,
  .apple-grid-3 {
    padding: 0 !important;
  }
}

/* Ant Design 组件样式覆盖 */
.ant-layout-header {
  background: var(--bg-secondary);
  padding: 0 24px;
  border-bottom: 1px solid var(--border-primary);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.ant-layout-content {
  min-height: calc(100vh - 64px - 70px);
  padding: 24px 0;
  width: 100%;
  display: flex;
  justify-content: center;
  background: var(--bg-primary);
  transition: background-color 0.3s ease;
}

.ant-layout-footer {
  background: var(--bg-tertiary);
  text-align: center;
  padding: 24px;
  width: 100%;
  border-top: 1px solid var(--border-primary);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.ant-menu-horizontal {
  border-bottom: none;
  background: transparent !important;
}

.ant-card {
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  background: var(--bg-card);
  border: 1px solid var(--border-secondary);
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: var(--shadow-md);
  transition: box-shadow 0.3s ease;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 错误状态样式 */
.error-container {
  text-align: center;
  padding: 40px 20px;
  color: #ff4d4f;
}

/* 空状态样式 */
.empty-container {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

/* 苹果风格按钮 */
.apple-button {
  background: var(--color-primary);
  color: var(--text-inverse);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: -0.01em;
}

.apple-button:hover {
  background: var(--color-primary);
  filter: brightness(0.9);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.apple-button-secondary {
  background: var(--bg-secondary);
  color: var(--color-primary);
  border: 1px solid var(--border-primary);
}

.apple-button-secondary:hover {
  background: var(--bg-tertiary);
  color: var(--color-primary);
}

/* 苹果风格输入框 */
.apple-input {
  border: 1px solid var(--border-tertiary);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.apple-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary), 0.1);
  outline: none;
}

/* 苹果风格标签 */
.apple-tag {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: inline-block;
  margin: 4px;
  transition: all 0.3s ease;
}

.apple-tag-primary {
  background: var(--color-primary);
  color: var(--text-inverse);
}

/* 苹果风格分割线 */
.apple-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-tertiary), transparent);
  margin: 32px 0;
  border: none;
}

/* 苹果风格阴影 */
.apple-shadow-sm {
  box-shadow: var(--shadow-sm);
}

.apple-shadow-md {
  box-shadow: var(--shadow-md);
}

.apple-shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* 苹果风格渐变背景 */
.apple-gradient-bg {
  background: var(--gradient-primary);
}

.apple-gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 苹果风格动画 */
@keyframes apple-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.apple-fade-in {
  animation: apple-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 苹果风格网格布局 */
.apple-grid {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.apple-grid-2 {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.apple-grid-3 {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* 响应式网格调整 */
@media (max-width: 768px) {
  .apple-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    justify-items: center;
  }

  .apple-grid-2 {
    grid-template-columns: 1fr;
    gap: 20px;
    justify-items: center;
  }

  .apple-grid-3 {
    grid-template-columns: 1fr;
    gap: 20px;
    justify-items: center;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .apple-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    justify-items: center;
  }

  .apple-grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    justify-items: center;
  }

  .apple-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    justify-items: center;
  }
}

@media (min-width: 1025px) {
  .apple-grid,
  .apple-grid-2,
  .apple-grid-3 {
    justify-items: stretch;
  }
}

/* 页面内容居中 - 最终解决方案 */
.ant-layout-content {
  display: flex !important;
  justify-content: center !important;
  align-items: flex-start !important;
}

.ant-layout-content > div {
  width: 100% !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 确保主要组件居中 */
.ant-carousel {
  margin: 0 auto !important;
}

.apple-grid,
.apple-grid-2,
.apple-grid-3 {
  margin: 0 auto !important;
}

.ant-row {
  margin: 0 auto !important;
}

/* 确保标签页内容居中 */
.ant-tabs-content-holder {
  display: flex !important;
  justify-content: center !important;
}

.ant-tabs-tabpane {
  width: 100% !important;
  max-width: 1200px !important;
}

/* 确保内容区域不会超出容器 */
* {
  box-sizing: border-box;
}

/* 最后的强制居中规则 */
body {
  margin: 0;
  padding: 0;
}

#root {
  width: 100%;
  margin: 0 auto;
}

.ant-layout {
  width: 100%;
}

