<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研究方向滚动测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            color: #1890ff;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        
        /* 模拟团队成员卡片中的研究方向区域 */
        .card-research-area {
            height: 120px; /* 模拟卡片中的固定高度 */
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 12px;
            background: #fafafa;
            display: flex;
            flex-direction: column;
            position: relative; /* 为渐变遮罩提供定位上下文 */
        }

        /* 错误的实现 - 用于对比 */
        .card-research-area-wrong {
            height: 120px;
            border: 1px solid #ff4d4f;
            border-radius: 6px;
            padding: 12px;
            background: #fff2f0;
            display: flex;
            flex-direction: column;
        }
        
        .research-title {
            font-size: 14px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 8px;
            flex: 0 0 auto;
        }
        
        .research-content {
            flex: 1 1 auto;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 100%;
            padding-right: 4px;
        }

        /* 错误的滚动容器 - 用于对比 */
        .research-content-wrong {
            flex: 1 1 auto;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 100%;
            position: relative; /* 错误：在滚动容器上设置 relative */
            padding-right: 4px;
        }
        
        /* 模拟弹窗中的研究方向区域 */
        .modal-research-area {
            max-height: 300px;
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            overflow-y: auto;
            overflow-x: hidden;
            position: relative;
            padding-right: 24px;
        }
        
        /* 滚动条样式 */
        .research-content::-webkit-scrollbar {
            width: 4px;
        }
        .research-content::-webkit-scrollbar-track {
            background: transparent;
        }
        .research-content::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 2px;
        }
        .research-content::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }
        
        .modal-research-area::-webkit-scrollbar {
            width: 6px;
        }
        .modal-research-area::-webkit-scrollbar-track {
            background: #f3f4f6;
            border-radius: 3px;
        }
        .modal-research-area::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
        }
        .modal-research-area::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }
        
        /* Firefox 滚动条样式 */
        .research-content {
            scrollbar-width: thin;
            scrollbar-color: #d1d5db transparent;
        }
        
        .modal-research-area {
            scrollbar-width: thin;
            scrollbar-color: #d1d5db #f3f4f6;
        }
        
        .content-text {
            font-size: 13px;
            color: #86868b;
            line-height: 1.5;
            margin: 0;
        }
        
        .modal-content-text {
            font-size: 15px;
            color: #1d1d1f;
            line-height: 1.6;
            margin: 0;
        }
        
        .gradient-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 4px; /* 避免覆盖滚动条 */
            height: 12px;
            background: linear-gradient(transparent, rgba(255,255,255,0.8));
            pointer-events: none;
            opacity: 0.6;
            z-index: 1;
        }

        /* 错误的渐变遮罩 - 用于对比 */
        .gradient-overlay-wrong {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 12px;
            background: linear-gradient(transparent, rgba(255,192,203,0.8)); /* 粉色用于区分 */
            pointer-events: none;
            opacity: 0.8;
        }
        
        .modal-gradient-overlay {
            position: absolute;
            bottom: 16px;
            left: 16px;
            right: 16px;
            height: 20px;
            background: linear-gradient(transparent, #f8f9fa);
            pointer-events: none;
            opacity: 0.8;
            border-radius: 0 0 12px 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>研究方向滚动功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 渐变遮罩定位修复对比</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div>
                    <h4 style="color: #ff4d4f; margin-bottom: 10px;">❌ 修复前（错误）</h4>
                    <div class="card-research-area-wrong">
                        <div class="research-title">🔬 研究方向</div>
                        <div class="research-content-wrong">
                            <div class="content-text">
                                <strong>主要研究领域：</strong><br><br>
                                • <strong>城市大数据分析</strong><br>
                                &nbsp;&nbsp;- 城市交通流量预测与优化<br>
                                &nbsp;&nbsp;- 城市人口流动模式分析<br>
                                &nbsp;&nbsp;- 智慧城市数据挖掘技术<br><br>
                                • <strong>地理人工智能 (GeoAI)</strong><br>
                                &nbsp;&nbsp;- 空间数据深度学习<br>
                                &nbsp;&nbsp;- 地理信息系统智能化<br>
                                &nbsp;&nbsp;- 遥感图像智能解译<br><br>
                                • <strong>机器学习与数据挖掘</strong><br>
                                &nbsp;&nbsp;- 时空数据挖掘算法<br>
                                &nbsp;&nbsp;- 图神经网络应用<br>
                                &nbsp;&nbsp;- 强化学习在城市规划中的应用
                            </div>
                            <!-- 错误：遮罩在滚动容器内部，会跟随内容滚动 -->
                            <div class="gradient-overlay-wrong"></div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 style="color: #52c41a; margin-bottom: 10px;">✅ 修复后（正确）</h4>
                    <div class="card-research-area">
                        <div class="research-title">🔬 研究方向</div>
                        <div class="research-content">
                            <div class="content-text">
                                <strong>主要研究领域：</strong><br><br>
                                • <strong>城市大数据分析</strong><br>
                                &nbsp;&nbsp;- 城市交通流量预测与优化<br>
                                &nbsp;&nbsp;- 城市人口流动模式分析<br>
                                &nbsp;&nbsp;- 智慧城市数据挖掘技术<br><br>
                                • <strong>地理人工智能 (GeoAI)</strong><br>
                                &nbsp;&nbsp;- 空间数据深度学习<br>
                                &nbsp;&nbsp;- 地理信息系统智能化<br>
                                &nbsp;&nbsp;- 遥感图像智能解译<br><br>
                                • <strong>机器学习与数据挖掘</strong><br>
                                &nbsp;&nbsp;- 时空数据挖掘算法<br>
                                &nbsp;&nbsp;- 图神经网络应用<br>
                                &nbsp;&nbsp;- 强化学习在城市规划中的应用
                            </div>
                        </div>
                        <!-- 正确：遮罩在外层容器，固定在底部 -->
                        <div class="gradient-overlay"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 团队成员卡片中的研究方向区域（固定高度 120px）</div>
            <div class="card-research-area">
                <div class="research-title">🔬 研究方向</div>
                <div class="research-content">
                    <div class="content-text">
                        <strong>主要研究领域：</strong><br><br>
                        
                        • <strong>城市大数据分析</strong><br>
                        &nbsp;&nbsp;- 城市交通流量预测与优化<br>
                        &nbsp;&nbsp;- 城市人口流动模式分析<br>
                        &nbsp;&nbsp;- 智慧城市数据挖掘技术<br><br>
                        
                        • <strong>地理人工智能 (GeoAI)</strong><br>
                        &nbsp;&nbsp;- 空间数据深度学习<br>
                        &nbsp;&nbsp;- 地理信息系统智能化<br>
                        &nbsp;&nbsp;- 遥感图像智能解译<br><br>
                        
                        • <strong>机器学习与数据挖掘</strong><br>
                        &nbsp;&nbsp;- 时空数据挖掘算法<br>
                        &nbsp;&nbsp;- 图神经网络应用<br>
                        &nbsp;&nbsp;- 强化学习在城市规划中的应用<br><br>
                        
                        • <strong>智能交通系统</strong><br>
                        &nbsp;&nbsp;- 交通信号优化<br>
                        &nbsp;&nbsp;- 自动驾驶路径规划<br>
                        &nbsp;&nbsp;- 交通事故预测与预防<br><br>
                        
                        • <strong>环境监测与分析</strong><br>
                        &nbsp;&nbsp;- 空气质量预测模型<br>
                        &nbsp;&nbsp;- 环境污染源识别<br>
                        &nbsp;&nbsp;- 生态系统健康评估
                    </div>
                    <div class="gradient-overlay"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. Mentor 详情弹窗中的研究方向区域（最大高度 300px）</div>
            <div class="modal-research-area">
                <div class="modal-content-text">
                    <h3 style="margin-top: 0; color: #1890ff;">详细研究方向</h3>
                    
                    <h4>🏙️ 城市大数据分析</h4>
                    <p>专注于利用大数据技术分析城市复杂系统，包括：</p>
                    <ul>
                        <li><strong>交通流量预测</strong>：基于历史数据和实时数据，使用深度学习模型预测城市交通流量变化</li>
                        <li><strong>人口流动分析</strong>：通过手机信令数据、社交媒体数据等分析城市人口流动规律</li>
                        <li><strong>城市功能区识别</strong>：利用POI数据、遥感数据等识别和分析城市功能区分布</li>
                    </ul>
                    
                    <h4>🤖 地理人工智能 (GeoAI)</h4>
                    <p>将人工智能技术与地理信息科学相结合：</p>
                    <ul>
                        <li><strong>空间深度学习</strong>：开发适用于空间数据的深度学习架构和算法</li>
                        <li><strong>智能GIS</strong>：构建具有自主学习和决策能力的地理信息系统</li>
                        <li><strong>遥感AI</strong>：利用计算机视觉技术自动解译卫星和航空遥感图像</li>
                    </ul>
                    
                    <h4>🧠 机器学习与数据挖掘</h4>
                    <p>专注于时空数据的机器学习方法研究：</p>
                    <ul>
                        <li><strong>时空数据挖掘</strong>：开发处理时空数据的新型算法和模型</li>
                        <li><strong>图神经网络</strong>：将图神经网络应用于城市网络分析</li>
                        <li><strong>强化学习</strong>：在城市规划和交通优化中应用强化学习技术</li>
                    </ul>
                    
                    <h4>🚗 智能交通系统</h4>
                    <p>致力于构建更智能、更高效的交通系统：</p>
                    <ul>
                        <li><strong>信号优化</strong>：基于实时交通数据的智能信号控制系统</li>
                        <li><strong>路径规划</strong>：为自动驾驶车辆开发最优路径规划算法</li>
                        <li><strong>事故预测</strong>：利用历史数据和环境因素预测交通事故风险</li>
                    </ul>
                    
                    <h4>🌱 环境监测与分析</h4>
                    <p>运用数据科学方法监测和分析环境变化：</p>
                    <ul>
                        <li><strong>空气质量预测</strong>：基于气象数据和污染源数据预测空气质量</li>
                        <li><strong>污染源识别</strong>：利用传感器网络和机器学习识别污染源</li>
                        <li><strong>生态评估</strong>：通过遥感数据和实地调查评估生态系统健康状况</li>
                    </ul>
                </div>
                <div class="modal-gradient-overlay"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 渐变遮罩定位修复说明</div>
            <div style="background: #f0f8ff; padding: 15px; border-radius: 6px; border-left: 4px solid #1890ff;">
                <h4 style="margin-top: 0; color: #ff4d4f;">❌ 修复前的问题：</h4>
                <ul>
                    <li><strong>DOM结构错误</strong>：渐变遮罩作为滚动容器的子元素</li>
                    <li><strong>定位错误</strong>：遮罩相对于滚动容器定位，会跟随内容滚动</li>
                    <li><strong>用户体验差</strong>：模糊效果"粘"在文本上，看起来很奇怪</li>
                </ul>

                <h4 style="color: #52c41a;">✅ 修复后的改进：</h4>
                <ul>
                    <li><strong>正确的DOM结构</strong>：遮罩移到外层容器，不在滚动容器内</li>
                    <li><strong>固定定位</strong>：遮罩始终固定在容器底部，不随内容滚动</li>
                    <li><strong>内容保护</strong>：增加 paddingBottom 避免内容被遮罩遮挡</li>
                    <li><strong>层级管理</strong>：使用 z-index 确保遮罩在内容之上</li>
                </ul>

                <h4>技术实现要点：</h4>
                <ul>
                    <li>🎯 <strong>定位上下文</strong>：在外层容器设置 position: relative</li>
                    <li>📍 <strong>绝对定位</strong>：遮罩使用 position: absolute 相对外层定位</li>
                    <li>🎨 <strong>视觉优化</strong>：调整 right 值避免覆盖滚动条</li>
                    <li>🔒 <strong>交互保护</strong>：pointer-events: none 确保不影响滚动</li>
                </ul>

                <h4>测试方法：</h4>
                <ol>
                    <li>在左侧（错误）区域滚动，观察粉色遮罩跟随文本移动</li>
                    <li>在右侧（正确）区域滚动，观察白色遮罩始终固定在底部</li>
                    <li>对比两种实现的视觉效果差异</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
