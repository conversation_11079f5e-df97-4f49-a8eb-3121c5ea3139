# 部署指南

## 🚀 快速启动

### 环境要求
- Node.js 18+
- Docker & Docker Compose
- Git
- PostgreSQL 17.0+ (推荐17.5)

### 1. 克隆项目
```bash
git clone <repository-url>
cd 课题组网站
```

### 2. 启动数据库
```bash
# 启动PostgreSQL数据库容器
docker-compose up -d postgres

# 验证数据库状态
docker ps | grep research_group_postgres
```

### 3. 配置后端环境
```bash
cd backend

# 复制环境变量模板
cp env.example .env

# 生成安全密钥（可选，已预配置）
node -e "console.log('APP_KEYS=' + require('crypto').randomBytes(32).toString('base64') + ',' + require('crypto').randomBytes(32).toString('base64'))"
node -e "console.log('API_TOKEN_SALT=' + require('crypto').randomBytes(16).toString('base64'))"
node -e "console.log('ADMIN_JWT_SECRET=' + require('crypto').randomBytes(32).toString('base64'))"
node -e "console.log('JWT_SECRET=' + require('crypto').randomBytes(32).toString('base64'))"
node -e "console.log('TRANSFER_TOKEN_SALT=' + require('crypto').randomBytes(16).toString('base64'))"
```

### 4. 安装依赖并启动服务

#### 后端服务
```bash
cd backend

# 安装依赖
npm install

# 安装PostgreSQL驱动（如果使用PostgreSQL）
npm install pg

# 启动开发服务器
npm run develop
```

#### 前端服务
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 5. 访问应用
- **前端网站**: http://localhost:5173
- **后端管理**: http://localhost:1337/admin
- **数据库**: localhost:5432

## 🐳 Docker 部署

### 完整Docker部署
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 单独启动服务
```bash
# 只启动数据库
docker-compose up -d postgres

# 只启动后端
docker-compose up -d backend

# 只启动前端
docker-compose up -d frontend
```

## 🔧 环境配置

### 数据库配置
```env
# PostgreSQL配置
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=research_group_website
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_SSL=false
```

### 安全配置
```env
# 应用密钥
APP_KEYS=your-app-keys-here
API_TOKEN_SALT=your-api-token-salt-here
ADMIN_JWT_SECRET=your-admin-jwt-secret-here
JWT_SECRET=your-jwt-secret-here
TRANSFER_TOKEN_SALT=your-transfer-token-salt-here
```

### 前端配置
```env
# API地址
VITE_API_URL=http://localhost:1337
```

## 📊 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| 前端 | 5173 | React开发服务器 |
| 后端 | 1337 | Strapi CMS |
| 数据库 | 5432 | PostgreSQL |

## 🔍 故障排除

### 数据库连接问题
```bash
# 检查数据库容器状态
docker ps | grep postgres

# 测试数据库连接
docker exec research_group_postgres psql -U postgres -d research_group_website -c "SELECT version();"

# 查看数据库日志
docker-compose logs postgres
```

### 后端启动问题
```bash
# 检查PostgreSQL驱动
npm list pg

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 清除缓存
npm run strapi cache:clean
```

### 前端启动问题
```bash
# 检查端口占用
lsof -i :5173

# 清除缓存
rm -rf node_modules/.vite
npm run dev
```

## 🚀 生产环境部署

### 1. 环境准备
```bash
# 设置生产环境变量
export NODE_ENV=production

# 构建前端
cd frontend
npm run build

# 构建后端
cd ../backend
npm run build
```

### 2. 使用Docker Compose生产配置
```bash
# 使用生产配置
docker-compose -f docker-compose.prod.yml up -d
```

### 3. 反向代理配置（Nginx示例）
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # 后端API
    location /api {
        proxy_pass http://localhost:1337;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 管理后台
    location /admin {
        proxy_pass http://localhost:1337;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📝 维护命令

### 数据库备份
```bash
# 备份数据库
docker exec research_group_postgres pg_dump -U postgres research_group_website > backup.sql

# 恢复数据库
docker exec -i research_group_postgres psql -U postgres research_group_website < backup.sql
```

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
```

### 服务重启
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
docker-compose restart frontend
```

## 🔐 安全建议

1. **修改默认密码**
   - 数据库密码
   - 管理员账户密码

2. **配置HTTPS**
   - 使用SSL证书
   - 配置反向代理

3. **防火墙设置**
   - 只开放必要端口
   - 限制数据库访问

4. **定期备份**
   - 数据库备份
   - 文件备份

5. **监控日志**
   - 定期检查错误日志
   - 监控系统资源使用 