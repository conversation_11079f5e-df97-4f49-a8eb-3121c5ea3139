version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:17-alpine
    container_name: research_group_postgres
    environment:
      POSTGRES_DB: research_group_website
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - research_group_network
    restart: unless-stopped

  # Strapi Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: research_group_backend
    environment:
      DATABASE_CLIENT: postgres
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: research_group_website
      DATABASE_USERNAME: postgres
      DATABASE_PASSWORD: password
      DATABASE_SSL: false
      NODE_ENV: development
    ports:
      - "1337:1337"
    volumes:
      - ./backend:/srv/app
      - /srv/app/node_modules
    depends_on:
      - postgres
    networks:
      - research_group_network
    restart: unless-stopped

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: research_group_frontend
    environment:
      VITE_API_URL: http://localhost:1337
      NODE_ENV: development
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - research_group_network
    restart: unless-stopped

volumes:
  postgres_data:
    name: research_group_postgres_data

networks:
  research_group_network:
    driver: bridge 